package cn.com.sinosoft.os.beijingexitapply.model;

import java.util.Date;

/**
 * �������� - ʵ����.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:41
 * @version V1.0
 */
public class BeijingExitApply implements java.io.Serializable {

	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// ����.
	private String id;

	// ����������.
	private String applyName;

	// ��������.
	private String identityType;

	// ����.
	private String department;

	// ��������.
	private Date applyDate;

	// ��ʼʱ��.
	private Date startDate;

	// ����ʱ��.
	private Date endDate;

	// ��������.
	private Date travelDays;

	// Ŀ�ĵ�.
	private String applyDestn;

	// ��������.
	private String travelReason;

	// 流程标识.
	private String piId;

	// 所领导.
	private String leader;

	// 审核状态1.
	private String auditState;

	// ���ӵ���.
	private String addZone;

	// ���ӻ���.
	private String addOrg;

	// ���ӿ���.
	private String addDep;

	// ������.
	private String addUser;

	// ����ʱ��.
	private Date addTime;

	// �޸ĵ���.
	private String modyZone;

	// �޸Ļ���.
	private String modyOrg;

	// �޸Ŀ���.
	private String modyDep;

	// �޸���.
	private String modyUser;

	// �޸�ʱ��.
	private Date modyTime;

	// �Ƿ���Ч.
	private String state;

	// ���ݽ���-������Դ.
	private String dataSource;

	// ���ݽ���-����ʱ��.
	private Date dataModyTime;

	/**
	 * ��ȡ ����.
	 *
	 * @return ����
	 */
	public String getId() {
		return id;
	}

	/**
	 * ���� ����.
	 *
	 * @param id
	 *			     ����
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * ��ȡ ����������.
	 *
	 * @return ����������
	 */
	public String getApplyName() {
		return applyName;
	}

	/**
	 * ���� ����������.
	 *
	 * @param applyName
	 *			     ����������
	 */
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}

	/**
	 * ��ȡ ��������.
	 *
	 * @return ��������
	 */
	public String getIdentityType() {
		return identityType;
	}

	/**
	 * ���� ��������.
	 *
	 * @param identityType
	 *			     ��������
	 */
	public void setIdentityType(String identityType) {
		this.identityType = identityType;
	}

	/**
	 * ��ȡ ����.
	 *
	 * @return ����
	 */
	public String getDepartment() {
		return department;
	}

	/**
	 * ���� ����.
	 *
	 * @param department
	 *			     ����
	 */
	public void setDepartment(String department) {
		this.department = department;
	}

	/**
	 * ��ȡ ��������.
	 *
	 * @return ��������
	 */
	public Date getApplyDate() {
		return applyDate;
	}

	/**
	 * ���� ��������.
	 *
	 * @param applyDate
	 *			     ��������
	 */
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	/**
	 * ��ȡ ��ʼʱ��.
	 *
	 * @return ��ʼʱ��
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * ���� ��ʼʱ��.
	 *
	 * @param startDate
	 *			     ��ʼʱ��
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * ��ȡ ����ʱ��.
	 *
	 * @return ����ʱ��
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * ���� ����ʱ��.
	 *
	 * @param endDate
	 *			     ����ʱ��
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * ��ȡ ��������.
	 *
	 * @return ��������
	 */
	public Date getTravelDays() {
		return travelDays;
	}

	/**
	 * ���� ��������.
	 *
	 * @param travelDays
	 *			     ��������
	 */
	public void setTravelDays(Date travelDays) {
		this.travelDays = travelDays;
	}

	/**
	 * ��ȡ Ŀ�ĵ�.
	 *
	 * @return Ŀ�ĵ�
	 */
	public String getApplyDestn() {
		return applyDestn;
	}

	/**
	 * ���� Ŀ�ĵ�.
	 *
	 * @param applyDestn
	 *			     Ŀ�ĵ�
	 */
	public void setApplyDestn(String applyDestn) {
		this.applyDestn = applyDestn;
	}

	/**
	 * ��ȡ ��������.
	 *
	 * @return ��������
	 */
	public String getTravelReason() {
		return travelReason;
	}

	/**
	 * ���� ��������.
	 *
	 * @param travelReason
	 *			     ��������
	 */
	public void setTravelReason(String travelReason) {
		this.travelReason = travelReason;
	}

	/**
	 * ��ȡ ���̱�ʶ.
	 *
	 * @return ���̱�ʶ
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * ���� ���̱�ʶ.
	 *
	 * @param piId
	 *			     ���̱�ʶ
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	/**
	 * ��ȡ ���״̬1.
	 *
	 * @return ���״̬1
	 */
	public String getAuditState() {
		return auditState;
	}

	/**
	 * ���� ���״̬1.
	 *
	 * @param auditState
	 *			     ���״̬1
	 */
	public void setAuditState(String auditState) {
		this.auditState = auditState;
	}

	/**
	 * ��ȡ ���ӵ���.
	 *
	 * @return ���ӵ���
	 */
	public String getAddZone() {
		return addZone;
	}

	/**
	 * ���� ���ӵ���.
	 *
	 * @param addZone
	 *			     ���ӵ���
	 */
	public void setAddZone(String addZone) {
		this.addZone = addZone;
	}

	/**
	 * ��ȡ ���ӻ���.
	 *
	 * @return ���ӻ���
	 */
	public String getAddOrg() {
		return addOrg;
	}

	/**
	 * ���� ���ӻ���.
	 *
	 * @param addOrg
	 *			     ���ӻ���
	 */
	public void setAddOrg(String addOrg) {
		this.addOrg = addOrg;
	}

	/**
	 * ��ȡ ���ӿ���.
	 *
	 * @return ���ӿ���
	 */
	public String getAddDep() {
		return addDep;
	}

	/**
	 * ���� ���ӿ���.
	 *
	 * @param addDep
	 *			     ���ӿ���
	 */
	public void setAddDep(String addDep) {
		this.addDep = addDep;
	}

	/**
	 * ��ȡ ������.
	 *
	 * @return ������
	 */
	public String getAddUser() {
		return addUser;
	}

	/**
	 * ���� ������.
	 *
	 * @param addUser
	 *			     ������
	 */
	public void setAddUser(String addUser) {
		this.addUser = addUser;
	}

	/**
	 * ��ȡ ����ʱ��.
	 *
	 * @return ����ʱ��
	 */
	public Date getAddTime() {
		return addTime;
	}

	/**
	 * ���� ����ʱ��.
	 *
	 * @param addTime
	 *			     ����ʱ��
	 */
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	/**
	 * ��ȡ �޸ĵ���.
	 *
	 * @return �޸ĵ���
	 */
	public String getModyZone() {
		return modyZone;
	}

	/**
	 * ���� �޸ĵ���.
	 *
	 * @param modyZone
	 *			     �޸ĵ���
	 */
	public void setModyZone(String modyZone) {
		this.modyZone = modyZone;
	}

	/**
	 * ��ȡ �޸Ļ���.
	 *
	 * @return �޸Ļ���
	 */
	public String getModyOrg() {
		return modyOrg;
	}

	/**
	 * ���� �޸Ļ���.
	 *
	 * @param modyOrg
	 *			     �޸Ļ���
	 */
	public void setModyOrg(String modyOrg) {
		this.modyOrg = modyOrg;
	}

	/**
	 * ��ȡ �޸Ŀ���.
	 *
	 * @return �޸Ŀ���
	 */
	public String getModyDep() {
		return modyDep;
	}

	/**
	 * ���� �޸Ŀ���.
	 *
	 * @param modyDep
	 *			     �޸Ŀ���
	 */
	public void setModyDep(String modyDep) {
		this.modyDep = modyDep;
	}

	/**
	 * ��ȡ �޸���.
	 *
	 * @return �޸���
	 */
	public String getModyUser() {
		return modyUser;
	}

	/**
	 * ���� �޸���.
	 *
	 * @param modyUser
	 *			     �޸���
	 */
	public void setModyUser(String modyUser) {
		this.modyUser = modyUser;
	}

	/**
	 * ��ȡ �޸�ʱ��.
	 *
	 * @return �޸�ʱ��
	 */
	public Date getModyTime() {
		return modyTime;
	}

	/**
	 * ���� �޸�ʱ��.
	 *
	 * @param modyTime
	 *			     �޸�ʱ��
	 */
	public void setModyTime(Date modyTime) {
		this.modyTime = modyTime;
	}

	/**
	 * ��ȡ �Ƿ���Ч.
	 *
	 * @return �Ƿ���Ч
	 */
	public String getState() {
		return state;
	}

	/**
	 * ���� �Ƿ���Ч.
	 *
	 * @param state
	 *			     �Ƿ���Ч
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * ��ȡ ���ݽ���-������Դ.
	 *
	 * @return ���ݽ���-������Դ
	 */
	public String getDataSource() {
		return dataSource;
	}

	/**
	 * ���� ���ݽ���-������Դ.
	 *
	 * @param dataSource
	 *			     ���ݽ���-������Դ
	 */
	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	/**
	 * ��ȡ ���ݽ���-����ʱ��.
	 *
	 * @return ���ݽ���-����ʱ��
	 */
	public Date getDataModyTime() {
		return dataModyTime;
	}

	/**
	 * ���� ���ݽ���-����ʱ��.
	 *
	 * @param dataModyTime
	 *			     ���ݽ���-����ʱ��
	 */
	public void setDataModyTime(Date dataModyTime) {
		this.dataModyTime = dataModyTime;
	}

	/**
	 * 获取 所领导.
	 *
	 * @return 所领导
	 */
	public String getLeader() {
		return leader;
	}

	/**
	 * 设置 所领导.
	 *
	 * @param leader
	 *			     所领导
	 */
	public void setLeader(String leader) {
		this.leader = leader;
	}


}