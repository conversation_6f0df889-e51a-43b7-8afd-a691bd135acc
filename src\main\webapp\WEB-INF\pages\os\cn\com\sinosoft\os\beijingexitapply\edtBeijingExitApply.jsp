<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>�������� - �༭</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<style type="text/css">
		.form-container {
			width: 80%;
			margin: 20px auto;
			padding: 20px;
			border: 1px solid #ddd;
			border-radius: 5px;
			background-color: #f9f9f9;
		}
		.form-title {
			text-align: center;
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 20px;
			color: #333;
		}
		.audit-section {
			margin-top: 20px;
			padding: 15px;
			border: 1px solid #ccc;
			border-radius: 5px;
			background-color: #fff;
		}
		.audit-title {
			font-weight: bold;
			color: #666;
			margin-bottom: 10px;
		}
	</style>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");

			// 根据页面状态设置表单
			var pageState = "${pageState}";
			if (pageState == "view") {
				// 查看状态下禁用表单编辑
				form.setEnabled(false);
			}

			// 自动全屏显示
			try {
				if (window.parent && window.parent.mini) {
					// 如果在mini框架中打开，自动最大化
					var win = window.parent.mini.getWindow(window);
					if (win && win.max) {
						win.max();
					}
				} else {
					// 如果是独立窗口直接打开，调整到屏幕大小
					if (window.screen && window.screen.availWidth && window.screen.availHeight) {
						window.resizeTo(window.screen.availWidth, window.screen.availHeight);
						window.moveTo(0, 0);
					}
				}
			} catch (e) {
				// 忽略可能的权限错误
				console.log("自动全屏显示受限：" + e.message);
			}
		});

		//保存
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//显示错误信息，宽度400高度300
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}

			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}

		//�ύ����
		function submit() {
			form.validate();
			if (form.isValid() == false) {
				//��ʾ������Ϣ������400���߶�300
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}

			mini.confirm("ȷ��Ҫ�ύ�������ύ�󽫽����������̡�", "ȷ��", function (action) {
				if (action == "ok") {
					// ����type����Ϊ2����ʾ�ύ��������������
					var typeInput = document.createElement("input");
					typeInput.type = "hidden";
					typeInput.name = "type";
					typeInput.value = "2";
					document.form1.appendChild(typeInput);

					if ("${pageState}" == "add") {
						document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
					} else {
						document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
					}
					document.form1.submit();
					waitClick();
				}
			});
		}

		//�����ύ
		function auditSubmit(auditResult) {
			var auditRemark = mini.get("auditRemark").getValue();
			if (!auditRemark) {
				mini.alert("����д�������");
				return;
			}

			document.form1.action = "${ctx}/auditBeijingExitApplySubmit.ac";
			document.getElementById("auditResult").value = auditResult;
			document.form1.submit();
		}
	</script>
</head>
<body>
	<form id="form1" name="form1" method="post">
		<div style="display:none;">
			<input type="hidden" name="id" value="${result.id}" />
			<input type="hidden" name="pageState" value="${pageState}" />
			<input type="hidden" id="auditResult" name="auditResult" value="" />
			<input type="hidden" name="taskid" value="${taskid}" />
			<input type="hidden" name="parama" value="${parama}" />
			<input type="hidden" name="piId" value="${piId}" />
		</div>

	<div class="form-container">
		<div class="form-title">出京申请表</div>
		<table class="tab-1" cellpadding="8" cellspacing="0" border="1" style="width:100%; border-collapse: collapse;">
		<COLGROUP>
			<col align="right" style="width:20%"/>
			<col align="left" style="width:30%"/>
			<col align="right" style="width:20%"/>
			<col align="left" style="width:30%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;申请人姓名
			</td>
			<td>
				<bspHtml:TextBox property="result.applyName"
					style="width:100%;"
					emptyText="请输入申请人姓名" maxLength="50" required="true"
					value="${sessionScope.SESSION_USERVIEW.name}"
					vtype="rangeChar:1,50;"
					rangeCharErrorText="[申请人姓名] 字符长度必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				性别
			</td>
			<td>
				<bspHtml:TextBox property="result.department"
					style="width:100%;"
					emptyText="请选择部门" maxLength="100" enabled="false"
					value="${sessionScope.SESSION_USERVIEW.departmentName}"
					vtype="rangeChar:0,100;"
					rangeCharErrorText="[部门] 字符长度必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;申请日期
			</td>
			<td>
				<bspHtml:DatePicker property="result.applyDate"
					style="width:100%;" required="true" enabled="false"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择申请日期">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;人员类型
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.identityType"
					url="IDENTITY_TYPE" style="width:100%;" required="true"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
		<c:if test="${sessionScope.SESSION_USERVIEW.position ne '10'}">
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;发往所领导：
			</td>
			<td colspan="3">
				<bspHtml:ComboBox property="result.leader"
					style="width:100%;"
					url="CM_ZGSLDSP_USERNAME"
					allowInput="false"
					viewState="edit"
					emptyText="人事处选择分管所领导,可以直接提交"
					textField="NAME" valueField="ID"
					requiredErrorText="[所领导] 不能为空">
				</bspHtml:ComboBox>
			</td>
		</tr>
		</c:if>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;开始日期
			</td>
			<td>
				<bspHtml:DatePicker property="result.startDate"
					style="width:100%;" required="true"
					format="yyyy-MM-dd" emptyText="请选择开始日期">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;结束日期
			</td>
			<td>
				<bspHtml:DatePicker property="result.endDate"
					style="width:100%;" required="true"
					format="yyyy-MM-dd" emptyText="请选择结束日期">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				出行天数
			</td>
			<td>
				<bspHtml:TextBox property="result.travelDays"
					style="width:100%;"
					emptyText="请输入出行天数" maxLength="10"
					vtype="int" intErrorText="请输入正确的数字">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;目的地
			</td>
			<td>
				<bspHtml:TextBox property="result.applyDestn"
					style="width:100%;" required="true"
					emptyText="请输入目的地" maxLength="100"
					vtype="rangeChar:1,100;"
					rangeCharErrorText="[目的地] 字符长度必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;出行事由
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.travelReason"
					style="width:100%;height:80px;" required="true"
					emptyText="请输入出行事由" maxLength="500"
					vtype="rangeChar:1,500;"
					rangeCharErrorText="[出行事由] 字符长度必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
		</tr>
		</table>

		<!-- 审批意见 -->
		<c:if test="${pageState eq 'view' and not empty taskid}">
		<div class="audit-section">
			<div class="audit-title">审批意见</div>
			<table class="tab-1" cellpadding="8" cellspacing="0" border="1" style="width:100%; border-collapse: collapse;">
				<COLGROUP>
					<col align="right" style="width:20%"/>
					<col align="left" style="width:80%"/>
				</COLGROUP>
				<tr>
					<td align="right" class="bgcolor">
						<font color="red">*</font>&nbsp;审批结果
					</td>
					<td>
						<bspHtml:TextArea property="auditRemark"
							style="width:100%;height:80px;" required="true"
							emptyText="�������������" maxLength="500"
							vtype="rangeChar:1,500;"
							rangeCharErrorText="[�������] �ַ����ȱ����� {0} �� {1} ֮��">
						</bspHtml:TextArea>
					</td>
				</tr>
			</table>
		</div>
		</c:if>

		<!-- ��ť���� -->
		<div style="text-align: center; margin-top: 20px;">
			<c:if test="${pageState eq 'add' or pageState eq 'edit'}">
				<a class="mini-button" iconCls="icon-save" onclick="save()">����</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-ok" onclick="submit()" style="background-color: #2196F3; color: white;">�ύ</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">ȡ��</a>
			</c:if>

			<c:if test="${pageState eq 'view' and not empty taskid}">
				<a class="mini-button" iconCls="icon-ok" onclick="auditSubmit('1')" style="background-color: #4CAF50; color: white;">����ͨ��</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="auditSubmit('dz')" style="background-color: #f44336; color: white;">����</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">ȡ��</a>
			</c:if>

			<c:if test="${pageState eq 'view' and empty taskid}">
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">�ر�</a>
			</c:if>
		</div>
	</div>
	</form>
</body>
</html>
