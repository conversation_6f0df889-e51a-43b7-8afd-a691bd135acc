package cn.com.sinosoft.os.beijingexitapply.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.weaf.toolkit.Util;
import ie.bsp.frame.exception.GeneralExceptionHandler;

/**
 * 出京申请 - Action.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

	/**
	 * 默认构造.
	 */
	public BeijingExitApplyAction() {
		moduleId = "";
	}

	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// 出京申请 - 接口.
	private BeijingExitApplyService service;

	// 出京申请 - 实体.
	private BeijingExitApply result;

	// 主键.
	private String id;

	// 参数.
	private String parama;

	// 任务ID.
	private String taskid;

	// 流程实例ID.
	private String piId;

	/**
	 * 获取 出京申请 - 接口.
	 *
	 * @return 出京申请 - 接口
	 */
	public BeijingExitApplyService getService() {
		return service;
	}

	/**
	 * 设置 出京申请 - 接口.
	 *
	 * @param service
	 *			     出京申请 - 接口
	 */
	public void setService(BeijingExitApplyService service) {
		this.service = service;
	}

	/**
	 * 获取 出京申请 - 实体.
	 *
	 * @return 出京申请 - 实体
	 */
	public BeijingExitApply getResult() {
		return result;
	}

	/**
	 * 设置 出京申请 - 实体.
	 *
	 * @param result
	 *			     出京申请 - 实体
	 */
	public void setResult(BeijingExitApply result) {
		this.result = result;
	}

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id
	 *			     主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * 获取 参数.
	 *
	 * @return 参数
	 */
	public String getParama() {
		return parama;
	}

	/**
	 * 设置 参数.
	 *
	 * @param parama
	 *			     参数
	 */
	public void setParama(String parama) {
		this.parama = parama;
	}

	/**
	 * 获取 任务ID.
	 *
	 * @return 任务ID
	 */
	public String getTaskid() {
		return taskid;
	}

	/**
	 * 设置 任务ID.
	 *
	 * @param taskid
	 *			     任务ID
	 */
	public void setTaskid(String taskid) {
		this.taskid = taskid;
	}

	/**
	 * 获取 流程实例ID.
	 *
	 * @return 流程实例ID
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * 设置 流程实例ID.
	 *
	 * @param piId
	 *			     流程实例ID
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "";
		result = service.get(id);
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {
		
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		funcId = "";
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		funcId = "";
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
	}

	/**
	 * 审批申请页面.
	 *
	 * @return SUCCESS
	 */
	public String auditParentInput() {
		result = service.getPiId(piId);
		return "success";
	}

	/**
	 * 审批申请提交.
	 */
	public void applySubmit() {
		service.appSubmit(id, parama, taskid, piId);
		methodSuccess(true); // 记录日志 并关闭窗口
	}

}