package cn.com.sinosoft.os.beijingexitapply.service.impl;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.bsp.frame.dao.CommonBaseDao;
import ie.bsp.core.bean.UserView;
import java.util.List;
import java.util.Date;
import org.apache.struts2.ServletActionContext;
import ie.bsp.ui.FrameConstant;
import ie.weaf.toolkit.Util;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.text.DecimalFormat;
import java.util.Calendar;
import cn.com.sinosoft.mywork.service.WorkflowService;
import cn.com.sinosoft.mywork.model.WorkFlowConstant;
import cn.com.sinosoft.os.constant.OsConstant;
import cn.com.sinosoft.constant.IcmisModels;

/**
 * �������� - service�ӿ�ʵ��.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyServiceImpl implements BeijingExitApplyService {

	// ͨ��dao.
	private CommonBaseDao dao;
	// ����������.
	private WorkflowService wfservice;

	/**
	 * ??? ???dao.
	 *
	 * @return ???dao
	 */
	public CommonBaseDao getDao() {
		return dao;
	}

	/**
	 * ???? ???dao.
	 *
	 * @param dao
	 *			     ???dao
	 */
	public void setDao(CommonBaseDao dao) {
		this.dao = dao;
	}

	/**
	 * ?????????????.
	 *
	 * @return ??????????
	 */
	public WorkflowService getWfservice() {
		return wfservice;
	}

	/**
	 * ??????????????.
	 *
	 * @param wfservice
	 *			     ??????????
	 */
	public void setWfservice(WorkflowService wfservice) {
		this.wfservice = wfservice;
	}

	@Override
	public BeijingExitApply get(String id) {
		return (BeijingExitApply) dao.get(BeijingExitApply.class,id);
	}

	@Override
	public void delete(String ids) {
		String[] arrId = ids.split(",");

		for (int i = 0; i < arrId.length; i++) {
			dao.excuteSql("update OS_BEIJING_EXIT_APPLY set STATE='0' where 1=1 "
					+ " and ID = '" + arrId[i] + "'");
		}
	}

	@Override
	public void save(BeijingExitApply result) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession().
				getAttribute(FrameConstant.SESSION_USERVIEW);
		result.setAddZone(user.getZonecode());
		result.setAddOrg(user.getOrgcode());
		result.setAddDep(user.getDepartmentId());
		result.setAddUser(user.getUsername());
		result.setAddTime(new Date());
		result.setModyZone(user.getZonecode());
		result.setModyOrg(user.getOrgcode());
		result.setModyDep(user.getDepartmentId());
		result.setModyUser(user.getUsername());
		result.setModyTime(new Date());
		result.setState("1");
		result.setId(ie.bsp.util.UUID.randomUUID().toString());

		// ??????????
		String type = ServletActionContext.getRequest().getParameter("type");
		if ("2".equals(type)) {
			// ????????????????
			Map<String, Object> map = new HashMap<String, Object>();
			startWorkFlow(result, user, map);
			result.setAuditState(OsConstant.AUDIT_STATE_ONE); // ?????
		}

		dao.save(result);
	}

	/**
	 * ??????????
	 */
	private void startWorkFlow(BeijingExitApply result, UserView user, Map<String, Object> map) {
		map.put(WorkFlowConstant.START_USER, result.getApplyName());
		map.put("DEP_CODE", user.getDepartmentId()); // 部门代码
		map.put(WorkFlowConstant.TASK_NAME, "出京申请-" + result.getApplyName()); // 任务名称

		// 添加所领导参数
		if (result.getLeader() != null && !result.getLeader().isEmpty()) {
			map.put("manager", result.getLeader()); // 所领导
		}

		// 根据人员类型确定审批流程
		String step = "1"; // 默认职工流程
		if ("2".equals(result.getIdentityType())) {
			step = "2"; // 中层领导流程
		}
		map.put("step", step);

		String targetFilePath = ServletActionContext.getRequest().getSession().getServletContext().getRealPath("/")
				+ IcmisModels.TEMP_FOLD + "/"; // 获取项目临时文件目录
		String pid = wfservice.getWorkflowBaseService().startProcess("beijingexitapply", map, targetFilePath); // 启动流程
		result.setPiId(pid); // 设置piid
	}

	@Override
	public void edit(BeijingExitApply result) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		result.setModyZone(user.getZonecode());
		result.setModyOrg(user.getOrgcode());
		result.setModyDep(user.getDepartmentId());
		result.setModyUser(user.getUsername());
		result.setModyTime(new Date());

		// ???????????????????
		String type = ServletActionContext.getRequest().getParameter("type");
		String taskid = ServletActionContext.getRequest().getParameter("taskid");
		String piId = result.getPiId();

		if ("2".equals(type)) {
			if (piId != null && !piId.isEmpty()) {
				// ?????????????????????
				Map<String, Object> vMap = new HashMap<String, Object>();
				vMap.put(WorkFlowConstant.DEP_CODE, user.getDepartmentId());
				vMap.put("manager", result.getApplyName());
				vMap.put("TASKNAME", "????????-" + result.getApplyName());

				wfservice.handleTaskWithMap(taskid, "1", "?????????????", "", vMap);
			} else {
				// ????????????
				Map<String, Object> map = new HashMap<String, Object>();
				startWorkFlow(result, user, map);
				result.setAuditState(OsConstant.AUDIT_STATE_ONE); // ?????
			}
		}

		dao.edit(result);
	}

	@Override
	public BeijingExitApply getPiId(String piId) {
		String hql = "from BeijingExitApply where piId = ?";
		List<BeijingExitApply> list = dao.qryByHql(hql);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public void appSubmit(String id, String parama, String taskid, String piId) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		BeijingExitApply result = get(id);
		String auditRemark = ServletActionContext.getRequest().getParameter("auditRemark");
		String auditResult = ServletActionContext.getRequest().getParameter("auditResult");

		Map<String, Object> map = new HashMap<String, Object>();
		String rejection = "";

		// ���ù���������
		map.put(WorkFlowConstant.START_USER, result.getApplyName());
		map.put("manager", user.getUsername());

		if ("1".equals(auditResult)) {
			// ����ͨ��
			result.setAuditState(OsConstant.AUDIT_STATE_TWO);
			wfservice.handleTaskWithMap(taskid, "1", auditRemark, rejection, map);
		} else if ("dz".equals(auditResult)) {
			// ���ض���
			rejection = "dz";
			map.put("step", "dz");
			map.put(WorkFlowConstant.START_USER, result.getApplyName());
			result.setAuditState(OsConstant.AUDIT_STATE_THREE);
			wfservice.handleTaskWithMap(taskid, "2", auditRemark, rejection, map);
		}

		dao.edit(result);
	}

	@Override
	public String numberGenerate() {
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH) + 1;

		String yearStr = String.valueOf(year);
		String monthStr = new DecimalFormat("00").format(month);

		// ????????????
		String hql = "select max(applyName) from BeijingExitApply where applyName like ?";
		String prefix = "CJ" + yearStr + monthStr;
		List<String> list = dao.qryByHql(hql);

		int maxNum = 0;
		if (list != null && list.size() > 0 && list.get(0) != null) {
			String maxApplyName = list.get(0);
			if (maxApplyName.length() >= prefix.length() + 3) {
				try {
					String numStr = maxApplyName.substring(prefix.length());
					maxNum = Integer.parseInt(numStr);
				} catch (NumberFormatException e) {
					maxNum = 0;
				}
			}
		}

		maxNum++;
		String numStr = new DecimalFormat("000").format(maxNum);
		return prefix + numStr;
	}

}