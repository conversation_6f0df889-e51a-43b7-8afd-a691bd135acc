<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
<%
	String title = "出京申请"; // 标题
%>
<html>
<head>
	<title><%=title%></title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<%@ include file="/common/taglibs.jsp"%>
	<style type="text/css">
	.myrow {
		 color: gray;
	}
	</style>
	<script type="text/javascript">
		var grid,form,perm = false;
		// 初始化数据
		$(document).ready(function() {
			mini.parse();

			var n = mini.get("layout1").getRegion("north");
			if(n){
				n.height=$("#qryTable").height()+35;
				mini.layout();
				form = new mini.Form("form1");
			}
			grid = mini.get("datagrid1");
			if (perm) {
				// 双击Grid行执行查看
				grid.on("rowdblclick", function(e) {
					view();
				});
			}
			grid.on("drawcell", function (e) {
				var record = e.record;
				//行样式设置
				if (record.STATE_CODE == '0') {
					e.rowCls = "myrow";
				}
			});
			search();

			// 自动全屏功能
			try {
				if (window.parent && window.parent.mini) {
					// 如果是在mini窗口中打开，自动最大化
					var win = window.parent.mini.getWindow(window);
					if (win && win.max) {
						win.max();
					}
				} else {
					// 如果是在浏览器中直接打开，尝试最大化浏览器窗口
					if (window.screen && window.screen.availWidth && window.screen.availHeight) {
						window.resizeTo(window.screen.availWidth, window.screen.availHeight);
						window.moveTo(0, 0);
					}
				}
			} catch (e) {
				// 忽略可能的权限错误
				console.log("自动全屏功能受限：" + e.message);
			}
		});


		// 查询数据
		function search() {
			if(form){
				form.validate();
				if (form.isValid() == false){
					showFormErrorTexts(form.getErrorTexts(),400,300);
					return;
				}
			}

			grid.load({
				session_APPLY_NAME : encodeURIComponent(mini.get("session_APPLY_NAME").getValue()), // 申请人姓名
				session_IDENTITY_TYPE : encodeURIComponent(mini.get("session_IDENTITY_TYPE").getValue()), // 人员类型
				session_APPLY_DATE_BEGIN : encodeURIComponent(mini.get("session_APPLY_DATE_BEGIN").getFormValue()), // 申请日期开始
				session_APPLY_DATE_END : encodeURIComponent(mini.get("session_APPLY_DATE_END").getFormValue()) // 申请日期结束
			},function(){
				//加载成功之后要做的事情
			});
		}

		// 查看
		function view() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('不能查看多条记录！');
				return;
			}
			var record = records[0];
			if (record) {
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_viewParent.ac?pageState=view&id="
						+ record.ID,
					title : "查看",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// 窗口关闭时执行
					}
				});
			 win.max();
			} else {
				mini.alert("请选择一条记录");
			}
		}

		// 新增
		function add() {
			var win = mini.open({
				url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentInput.ac?pageState=add",
				title : "新增",
				width : 800,
				height : 400,
				showMaxButton : true,
				ondestroy : function(action) {
					// 窗口关闭时执行
					if (action == "save"){
						grid.reload();
					}
				}
			});
			 win.max();
		}

		// 修改
		function edit() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('不能修改多条记录！');
				return;
			}
			var record = records[0];
			if (record) {
				// 检查审核状态
				// 只有待审核状态才能修改
				//审核状态检查
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentInput.ac?pageState=edit&id="
						+ record.ID,
					title : "修改",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// 窗口关闭时执行
						if (action == "save"){
							grid.reload();
						}
					}
				});
				 win.max();
			} else {
				mini.alert("请选择一条记录");
			}
		}

		// 删除
		function remove() {
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				// 检查审核状态
				// 只有待审核状态才能删除
				// 删除确认
				mini.confirm ("确定要删除选中的" + rows.length + "条记录吗？","删除确认",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//执行删除
							grid.loading("删除中，请稍候......");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_delParentSubmit.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("请选择要删除的记录");
			}
		}

		// 审核状态渲染
		function onAuditStateRenderer(e) {
			var record = e.record;
			var value = e.value;
			if (value == "0") {
				return '<span style="color: orange;">待审核</span>';
			} else if (value == "1") {
				return '<span style="color: green;">已通过</span>';
			} else if (value == "2") {
				return '<span style="color: red;">已驳回</span>';
			} else {
				return '<span style="color: gray;">未知</span>';
			}
		}
	</script>
</head>
<body>
	<!-- 导出Excel相关HTML start -->
	<form id="excelForm" enctype="multipart/form-data"
		action="${ctx }/util/util_exportExcle.ac" method="post"
		target="excelIFrame" style="display:none;">
		<!-- 表头数据信息 -->
		<input type="hidden" name="headData" id="headData" />
		<!-- 表格列表数据，即当前目前已分页的 -->
		<input type="hidden" name="bodyData" id="bodyData" />
		<!-- 导出时要隐藏显示的字段field，逗号分隔 -->
		<input type="hidden" name="export_hideColumn" id="export_hideColumn" value="" />
		<!-- 要导出的所有字段field，逗号分隔 -->
		<input type="hidden" name="export_showColumn" id="export_showColumn" value=""/>
	</form>
	<iframe id="excelIFrame" name="excelIFrame" style="display:none;"></iframe>
	<!-- 导出Excel相关HTML end -->
	<div id="layout1" class="mini-layout" style="width: 100%; height: 100%;">
		<div title="<%=title%>" region="north" height="120" showHeader="true" class="util_search" >
			<div id="form1" style="padding: 0;margin: 0;">
				<table id="qryTable" align="center" style="margin-top: 6px;" cellpadding="4">
					<tr>
						<td align="right"><nobr>&nbsp;申请人：</nobr></td>
						<td>
							<bspHtml:TextBox property="session_APPLY_NAME"
								onenter="search()" vtype="maxLength:50;rangeChar:0,50;" maxLength="50"
								maxLengthErrorText="[申请人] 不能超过 {0} 个字符"
								rangeCharErrorText="[申请人] 字符长度必须在 {0} 到 {1} 之间" style="width:120px;">
							</bspHtml:TextBox>
						</td>
						<td align="right"><nobr>&nbsp;&nbsp;人员类型：</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_IDENTITY_TYPE"
								url="IDENTITY_TYPE" style="width:120px;"
								onenter="search()">
							</bspHtml:ComboBox>
						</td>
						<td align="right"><nobr>&nbsp;&nbsp;申请日期：</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_DATE_BEGIN"
								format="yyyy-MM-dd"
								value="<%=Util.getMonthFirstDay()%>" style="width:120px;">
							</bspHtml:DatePicker>
						</td>
						<td align="center"><nobr>&nbsp;至&nbsp;</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_DATE_END"
								format="yyyy-MM-dd"
								style="width:120px;">
							</bspHtml:DatePicker>
						</td>
						<td align="center">
							<a class="mini-button" iconCls="icon-search" onclick="search()">查询</a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<div title="<%=title%>" region="center" showHeader="false">
			<div class="mini-toolbar" style="padding: 2px;" borderStyle="border-left:0;border-top:0;border-right:0;">
					<script>perm = true;</script>
					<a class="mini-button" iconCls="icon-node" plain="true" onclick="view()">查看</a>
					<a class="mini-button" iconCls="icon-add" plain="true" onclick="add()">新增</a>
					<a class="mini-button" iconCls="icon-edit" plain="true" onclick="edit()">修改</a>
					<a class="mini-button" iconCls="icon-remove" plain="true" onclick="remove()">删除</a>
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">导出Excel</a>
			</div>
			<!--数据列表-->
			<div class="mini-fit">
				<div id="datagrid1" class="mini-datagrid" idField="ID" sortMode="client"
					 allowAlternating="true" url="${ctx }/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApplyList.ac"
					 style="width: 100%; height: 100%;" sizeList="[20,50,100]" pageSize="20"
					 multiSelect="true" borderStyle="border:0" selectOnLoad="true">
					<div property="columns">
						<div type="checkcolumn" align="center" headerAlign="center" width="50"></div>
						<div type="indexcolumn" align="center" headerAlign="center" width="50">序号</div>

						<div field="APPLY_NAME" width="100" headerAlign="center" align="center">申请人姓名</div>
						<div field="DEPARTMENT" width="120" headerAlign="center" align="center">部门</div>
						<div field="APPLY_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">申请日期</div>
						<div field="START_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">开始时间</div>
						<div field="END_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">结束时间</div>
						<div field="TRAVEL_DAYS" width="80" headerAlign="center" align="center">出行天数</div>
						<div field="APPLY_DESTN" width="120" headerAlign="center" align="center">目的地</div>
						<div field="TRAVEL_REASON" width="200" headerAlign="center" align="left">出行事由</div>
						<div field="AUDIT_STATE" width="100" headerAlign="center" align="center" renderer="onAuditStateRenderer">审核状态</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>